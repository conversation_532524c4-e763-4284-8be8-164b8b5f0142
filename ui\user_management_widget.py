#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTableWidget, QTableWidgetItem, QPushButton, 
                             QMessageBox, QDialog, QLineEdit, QComboBox,
                             QFormLayout, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from database import Database

class AddUserDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """إعداد واجهة إضافة مستخدم"""
        self.setWindowTitle("إضافة مستخدم جديد")
        self.setFixedSize(400, 350)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان
        title_label = QLabel("إضافة مستخدم جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        
        # الاسم الكامل
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("أدخل الاسم الكامل")
        form_layout.addRow("الاسم الكامل:", self.full_name_input)
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # الدور - إصلاح المشكلة هنا
        self.role_combo = QComboBox()
        
        # تعريف الأدوار مع الأسماء العربية
        self.roles_data = [
            ("admin", "مدير"),
            ("operator", "مشغل"),
            ("analyst", "محلل"),
            ("viewer", "مشاهد")
        ]
        
        # إضافة الأسماء العربية فقط إلى ComboBox
        arabic_names = [role[1] for role in self.roles_data]
        self.role_combo.addItems(arabic_names)
        
        form_layout.addRow("الدور:", self.role_combo)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_user)
        self.save_button.setDefault(True)
        buttons_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تركيز على حقل الاسم الكامل
        self.full_name_input.setFocus()
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #0056b3;
            }
            
            QPushButton:pressed {
                background-color: #004085;
            }
            
            QPushButton#cancel_button {
                background-color: #6c757d;
            }
            
            QPushButton#cancel_button:hover {
                background-color: #545b62;
            }
        """)
        
        # تطبيق معرف للزر إلغاء
        self.cancel_button.setObjectName("cancel_button")
    
    def save_user(self):
        """حفظ المستخدم الجديد"""
        full_name = self.full_name_input.text().strip()
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # الحصول على الدور الإنجليزي من الفهرس المحدد
        role_index = self.role_combo.currentIndex()
        if role_index >= 0 and role_index < len(self.roles_data):
            role = self.roles_data[role_index][0]  # الحصول على القيمة الإنجليزية
        else:
            role = "viewer"  # قيمة افتراضية
        
        if not all([full_name, username, password]):
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        if len(password) < 6:
            QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
        
        # حفظ المستخدم في قاعدة البيانات
        db = Database()
        user_id = db.add_user(username, password, role, full_name)
        
        if user_id:
            QMessageBox.information(self, "نجح", "تم إضافة المستخدم بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في إضافة المستخدم. اسم المستخدم موجود مسبق")
    
    def get_user_data(self):
        """الحصول على بيانات المستخدم"""
        role_index = self.role_combo.currentIndex()
        if role_index >= 0 and role_index < len(self.roles_data):
            role = self.roles_data[role_index][0]  # الحصول على القيمة الإنجليزية
        else:
            role = "viewer"  # قيمة افتراضية
        
        return {
            'full_name': self.full_name_input.text().strip(),
            'username': self.username_input.text().strip(),
            'role': role
        }


class UserManagementWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.db = Database()
        self.setup_ui()
        self.apply_styles()
        self.load_users()
    
    def setup_ui(self):
        """إعداد واجهة إدارة المستخدمين"""
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المستخدمين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.add_user_button = QPushButton("إضافة مستخدم")
        self.add_user_button.clicked.connect(self.add_user)
        toolbar_layout.addWidget(self.add_user_button)
        
        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_users)
        toolbar_layout.addWidget(self.refresh_button)
        
        toolbar_layout.addStretch()
        
        self.delete_user_button = QPushButton("حذف المستخدم")
        self.delete_user_button.clicked.connect(self.delete_user)
        self.delete_user_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_user_button)
        
        layout.addLayout(toolbar_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels([
            "المعرف", "اسم المستخدم", "الاسم الكامل", "الدور", "تاريخ الإنشاء"
        ])
        
        # إعداد الجدول
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.users_table)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
            }
            
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #0056b3;
            }
            
            QPushButton:pressed {
                background-color: #004085;
            }
            
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
            
            QPushButton#delete_user_button {
                background-color: #dc3545;
            }
            
            QPushButton#delete_user_button:hover {
                background-color: #c82333;
            }
            
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
                gridline-color: #dee2e6;
                selection-background-color: #007bff;
                selection-color: white;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            
            QTableWidget::item:alternate {
                background-color: #f8f9fa;
            }
            
            QHeaderView::section {
                background-color: #e9ecef;
                color: #495057;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # تطبيق معرف للزر حذف
        self.delete_user_button.setObjectName("delete_user_button")
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        users = self.db.get_users()
        
        self.users_table.setRowCount(len(users))
        
        role_names = {
            'admin': 'مدير',
            'operator': 'مشغل',
            'analyst': 'محلل',
            'viewer': 'مشاهد'
        }
        
        for row, user in enumerate(users):
            self.users_table.setItem(row, 0, QTableWidgetItem(str(user['id'])))
            self.users_table.setItem(row, 1, QTableWidgetItem(user['username']))
            self.users_table.setItem(row, 2, QTableWidgetItem(user['full_name']))
            self.users_table.setItem(row, 3, QTableWidgetItem(role_names.get(user['role'], user['role'])))
            self.users_table.setItem(row, 4, QTableWidgetItem(user['created_at']))
            
            # جعل الخلايا غير قابلة للتعديل
            for col in range(5):
                item = self.users_table.item(row, col)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = AddUserDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()
    
    def delete_user(self):
        """حذف المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            return
        
        user_id = int(self.users_table.item(current_row, 0).text())
        username = self.users_table.item(current_row, 1).text()
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم '{username}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.db.delete_user(user_id):
                QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حذف المستخدم")
    
    def on_selection_changed(self):
        """تفعيل/تعطيل أزرار التحكم حسب التحديد"""
        has_selection = len(self.users_table.selectedItems()) > 0
        self.delete_user_button.setEnabled(has_selection)



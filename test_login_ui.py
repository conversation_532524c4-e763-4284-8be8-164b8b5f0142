#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from ui.simple_login_dialog import SimpleLoginDialog

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    dialog = SimpleLoginDialog()
    
    print("عرض نافذة تسجيل الدخول...")
    result = dialog.exec_()
    
    if result == dialog.Accepted:
        user_data = dialog.get_user_data()
        print(f"تم تسجيل الدخول بنجاح: {user_data['full_name']}")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

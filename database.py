import sqlite3
import os
from datetime import datetime
from passlib.hash import bcrypt

class Database:
    def __init__(self, db_path="border_management.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال جديد بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء الجداول المطلوبة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول نقاط التفتيش
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS checkpoints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المركبات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vehicles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT NOT NULL UNIQUE,
                vehicle_type TEXT,
                model TEXT,
                color TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الأشخاص
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS persons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_number TEXT NOT NULL UNIQUE,
                name TEXT NOT NULL,
                nationality TEXT,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'operator', 'analyst', 'viewer')),
                full_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول الحركات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                checkpoint_id INTEGER NOT NULL,
                vehicle_id INTEGER,
                person_id INTEGER,
                direction TEXT NOT NULL CHECK (direction IN ('دخول', 'خروج')),
                movement_time TIMESTAMP NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (checkpoint_id) REFERENCES checkpoints (id),
                FOREIGN KEY (vehicle_id) REFERENCES vehicles (id),
                FOREIGN KEY (person_id) REFERENCES persons (id)
            )
        ''')
        
        # إنشاء فهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_checkpoint ON movements(checkpoint_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_vehicle ON movements(vehicle_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_person ON movements(person_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_movements_time ON movements(movement_time)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_vehicles_plate ON vehicles(plate_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_persons_identity ON persons(identity_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        
        conn.commit()
        
        # إدراج بيانات أولية
        self.insert_initial_data(cursor)
        conn.commit()
        conn.close()
    
    def insert_initial_data(self, cursor):
        """إدراج بيانات أولية"""
        # نقاط تفتيش أولية
        checkpoints = [
            ('البوابة الرئيسية', 'نقطة التفتيش الرئيسية'),
            ('البوابة الشمالية', 'نقطة التفتيش الشمالية'),
            ('البوابة الجنوبية', 'نقطة التفتيش الجنوبية'),
        ]

        for name, desc in checkpoints:
            cursor.execute('''
                INSERT OR IGNORE INTO checkpoints (name, description)
                VALUES (?, ?)
            ''', (name, desc))

        # إنشاء مستخدم مدير افتراضي
        admin_password = bcrypt.hash("admin123")
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, role, full_name)
            VALUES (?, ?, ?, ?)
        ''', ("admin", admin_password, "admin", "مدير النظام"))
    
    def add_checkpoint(self, name, description=""):
        """إضافة نقطة تفتيش جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO checkpoints (name, description) 
                VALUES (?, ?)
            ''', (name, description))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def add_vehicle(self, plate_number, vehicle_type="", model="", color=""):
        """إضافة مركبة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO vehicles (plate_number, vehicle_type, model, color) 
                VALUES (?, ?, ?, ?)
            ''', (plate_number, vehicle_type, model, color))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def add_person(self, identity_number, name, nationality="", phone=""):
        """إضافة شخص جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO persons (identity_number, name, nationality, phone) 
                VALUES (?, ?, ?, ?)
            ''', (identity_number, name, nationality, phone))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def add_movement(self, checkpoint_id, vehicle_id, person_id, direction, movement_time, notes=""):
        """إضافة حركة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO movements (checkpoint_id, vehicle_id, person_id, direction, movement_time, notes) 
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (checkpoint_id, vehicle_id, person_id, direction, movement_time, notes))
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()
    
    def get_checkpoints(self):
        """الحصول على جميع نقاط التفتيش"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM checkpoints ORDER BY name')
        result = cursor.fetchall()
        conn.close()
        return [dict(row) for row in result]
    
    def get_vehicle_by_plate(self, plate_number):
        """البحث عن مركبة بواسطة رقم اللوحة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM vehicles WHERE plate_number = ?', (plate_number,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def get_person_by_identity(self, identity_number):
        """البحث عن شخص بواسطة رقم الهوية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM persons WHERE identity_number = ?', (identity_number,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def get_movements(self, limit=100, offset=0, plate_filter="", date_filter=""):
        """الحصول على الحركات مع إمكانية التصفية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT 
                m.id,
                c.name as checkpoint_name,
                v.plate_number,
                p.name as person_name,
                p.identity_number,
                m.direction,
                m.movement_time,
                m.notes
            FROM movements m
            LEFT JOIN checkpoints c ON m.checkpoint_id = c.id
            LEFT JOIN vehicles v ON m.vehicle_id = v.id
            LEFT JOIN persons p ON m.person_id = p.id
            WHERE 1=1
        '''
        
        params = []
        
        if plate_filter:
            query += ' AND v.plate_number LIKE ?'
            params.append(f'%{plate_filter}%')
        
        if date_filter:
            query += ' AND DATE(m.movement_time) = ?'
            params.append(date_filter)
        
        query += ' ORDER BY m.movement_time DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        result = cursor.fetchall()
        conn.close()
        # تحويل النتائج إلى قوائم من القواميس
        return [dict(row) for row in result]

    # وظائف إدارة المستخدمين
    def add_user(self, username, password, role, full_name):
        """إضافة مستخدم جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            password_hash = bcrypt.hash(password)
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', (username, password_hash, role, full_name))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()

    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()

        if user and bcrypt.verify(password, user['password_hash']):
            # تحويل sqlite3.Row إلى قاموس عادي
            return dict(user)
        return None

    def get_users(self):
        """الحصول على جميع المستخدمين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, role, full_name, created_at FROM users ORDER BY username')
        result = cursor.fetchall()
        conn.close()
        # تحويل النتائج إلى قوائم من القواميس
        return [dict(row) for row in result]

    def delete_user(self, user_id):
        """حذف مستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()

    def update_user_password(self, user_id, new_password):
        """تحديث كلمة مرور المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            password_hash = bcrypt.hash(new_password)
            cursor.execute('UPDATE users SET password_hash = ? WHERE id = ?', (password_hash, user_id))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()

    def safe_dict_from_row(self, row):
        """تحويل آمن من sqlite3.Row إلى قاموس"""
        if row is None:
            return None
        try:
            return dict(row)
        except Exception as e:
            # في حالة فشل التحويل، إنشاء قاموس يدوياً
            result = {}
            for key in row.keys():
                result[key] = row[key]
            return result

    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
        result = cursor.fetchone()
        conn.close()
        return self.safe_dict_from_row(result)



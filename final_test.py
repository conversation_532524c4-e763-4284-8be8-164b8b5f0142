#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي شامل للنظام
"""

import sys
import traceback
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

def test_complete_system():
    """اختبار النظام الكامل"""
    print("اختبار النظام الكامل...")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        # اختبار النافذة الرئيسية
        from ui.main_window import MainWindow
        main_window = MainWindow()
        print("✓ النافذة الرئيسية: تم إنشاؤها بنجاح")
        
        # اختبار التبديل بين الصفحات
        main_window.switch_page(0)  # صفحة إدخال الحركة
        print("✓ التبديل إلى صفحة إدخال الحركة: نجح")
        
        main_window.switch_page(1)  # صفحة عرض الحركات
        print("✓ التبديل إلى صفحة عرض الحركات: نجح")
        
        # اختبار تحميل البيانات
        movements_page = main_window.movements_view_page
        movements_page.load_movements()
        print("✓ تحميل الحركات: نجح")
        
        # اختبار تحميل نقاط التفتيش
        entry_page = main_window.movement_entry_page
        entry_page.load_checkpoints()
        print("✓ تحميل نقاط التفتيش: نجح")
        
        # عرض النافذة لثانية واحدة
        main_window.show()
        app.processEvents()
        
        print("✓ عرض النافذة: نجح")
        
        main_window.close()
        print("✓ إغلاق النافذة: نجح")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار النظام: {e}")
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\nاختبار عمليات قاعدة البيانات...")
    print("-" * 30)
    
    try:
        from models.movement_model import MovementModel
        model = MovementModel()
        
        # اختبار الحصول على نقاط التفتيش
        checkpoints = model.get_checkpoints_list()
        print(f"✓ نقاط التفتيش: {len(checkpoints)} نقطة")
        
        # اختبار الحصول على الحركات
        movements = model.get_movements_list(limit=5)
        print(f"✓ الحركات: {len(movements)} حركة")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_excel_export():
    """اختبار تصدير Excel"""
    print("\nاختبار تصدير Excel...")
    print("-" * 20)
    
    try:
        from utils.excel_exporter import ExcelExporter
        from models.movement_model import MovementModel
        
        exporter = ExcelExporter()
        model = MovementModel()
        
        # الحصول على بعض البيانات
        movements = model.get_movements_list(limit=5)
        
        if movements:
            # اختبار تصدير البيانات
            filepath = exporter.export_movements_to_excel(movements)
            print(f"✓ تصدير البيانات: {filepath}")
            
            # اختبار تصدير التقرير الملخص
            summary_path = exporter.export_summary_report(movements)
            print(f"✓ تصدير التقرير الملخص: {summary_path}")
        else:
            print("⚠ لا توجد بيانات للتصدير")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار تصدير Excel: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نهائي شامل لنظام إدارة المعبر الحدودي")
    print("=" * 60)
    
    all_tests_passed = True
    
    # اختبار قاعدة البيانات
    if not test_database_operations():
        all_tests_passed = False
    
    # اختبار تصدير Excel
    if not test_excel_export():
        all_tests_passed = False
    
    # اختبار النظام الكامل
    if not test_complete_system():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام")
        print("🚀 يمكنك تشغيل التطبيق باستخدام: python main.py")
    else:
        print("❌ بعض الاختبارات فشلت")
        print("⚠ يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    return all_tests_passed

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"خطأ عام: {e}")
        traceback.print_exc()
        sys.exit(1)
    
    input("\nاضغط Enter للخروج...")

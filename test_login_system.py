#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام تسجيل الدخول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from database import Database
from ui.login_dialog import LoginDialog

def test_database():
    """اختبار قاعدة البيانات"""
    print("اختبار قاعدة البيانات...")
    
    db = Database()
    
    # اختبار إضافة مستخدم
    user_id = db.add_user("test_user", "test123", "operator", "مستخدم تجريبي")
    if user_id:
        print(f"✓ تم إنشاء مستخدم تجريبي بمعرف: {user_id}")
    else:
        print("✗ فشل في إنشاء المستخدم التجريبي")
    
    # اختبار التحقق من المستخدم
    user = db.authenticate_user("admin", "admin123")
    if user:
        print(f"✓ تم التحقق من المستخدم المدير: {user['full_name']}")
    else:
        print("✗ فشل في التحقق من المستخدم المدير")
    
    # اختبار الحصول على قائمة المستخدمين
    users = db.get_users()
    print(f"✓ عدد المستخدمين في النظام: {len(users)}")
    
    for user in users:
        print(f"  - {user['username']} ({user['full_name']}) - {user['role']}")
    
    print("انتهى اختبار قاعدة البيانات\n")

def test_login_dialog():
    """اختبار نافذة تسجيل الدخول"""
    print("اختبار نافذة تسجيل الدخول...")
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    login_dialog = LoginDialog()
    print("✓ تم إنشاء نافذة تسجيل الدخول بنجاح")
    
    # يمكن عرض النافذة للاختبار اليدوي
    # login_dialog.show()
    # app.exec_()
    
    print("انتهى اختبار نافذة تسجيل الدخول\n")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=== اختبار نظام تسجيل الدخول ===\n")
    
    try:
        # اختبار قاعدة البيانات
        test_database()
        
        # اختبار نافذة تسجيل الدخول
        test_login_dialog()
        
        print("=== انتهت جميع الاختبارات بنجاح ===")
        
    except Exception as e:
        print(f"خطأ أثناء الاختبار: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

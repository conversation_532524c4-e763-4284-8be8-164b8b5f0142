#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار واجهات المستخدم
"""

import sys
import traceback
from PyQt5.QtWidgets import QApplication

def test_imports():
    """اختبار استيراد الوحدات"""
    print("اختبار الاستيرادات...")
    
    try:
        from ui.main_window import MainWindow
        print("✓ MainWindow: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"✗ MainWindow: خطأ في الاستيراد - {e}")
        traceback.print_exc()
        return False
    
    try:
        from ui.movement_entry_widget import MovementEntryWidget
        print("✓ MovementEntryWidget: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"✗ MovementEntryWidget: خطأ في الاستيراد - {e}")
        traceback.print_exc()
        return False
    
    try:
        from ui.movements_view_widget import MovementsViewWidget
        print("✓ MovementsViewWidget: تم الاستيراد بنجاح")
    except Exception as e:
        print(f"✗ MovementsViewWidget: خطأ في الاستيراد - {e}")
        traceback.print_exc()
        return False
    
    return True

def test_widget_creation():
    """اختبار إنشاء الواجهات"""
    print("\nاختبار إنشاء الواجهات...")
    
    app = QApplication(sys.argv)
    
    try:
        from ui.movement_entry_widget import MovementEntryWidget
        widget = MovementEntryWidget()
        print("✓ MovementEntryWidget: تم الإنشاء بنجاح")
        widget.close()
    except Exception as e:
        print(f"✗ MovementEntryWidget: خطأ في الإنشاء - {e}")
        traceback.print_exc()
        return False
    
    try:
        from ui.movements_view_widget import MovementsViewWidget
        widget = MovementsViewWidget()
        print("✓ MovementsViewWidget: تم الإنشاء بنجاح")
        widget.close()
    except Exception as e:
        print(f"✗ MovementsViewWidget: خطأ في الإنشاء - {e}")
        traceback.print_exc()
        return False
    
    try:
        from ui.main_window import MainWindow
        window = MainWindow()
        print("✓ MainWindow: تم الإنشاء بنجاح")
        window.close()
    except Exception as e:
        print(f"✗ MainWindow: خطأ في الإنشاء - {e}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("اختبار واجهات المستخدم")
    print("=" * 40)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return False
    
    # اختبار إنشاء الواجهات
    if not test_widget_creation():
        print("\n❌ فشل في اختبار إنشاء الواجهات")
        return False
    
    print("\n✅ جميع الاختبارات نجحت!")
    print("يمكنك الآن تشغيل التطبيق بأمان باستخدام: python main.py")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"خطأ عام في الاختبار: {e}")
        traceback.print_exc()
        sys.exit(1)
    
    input("\nاضغط Enter للخروج...")

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHB<PERSON>Layout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QDateEdit,
                             QLabel, QMessageBox, QHeaderView, QGroupBox, QComboBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
from models.movement_model import MovementModel
from utils.excel_exporter import ExcelExporter
from datetime import datetime
import os

class MovementsViewWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.movement_model = MovementModel()
        self.excel_exporter = ExcelExporter()
        self.current_data = []
        self.setup_ui()
        self.load_movements()
    
    def setup_ui(self):
        """إعداد واجهة عرض الحركات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("عرض وإدارة الحركات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # مجموعة البحث والتصفية مع تحسين المرونة
        search_group = QGroupBox("البحث والتصفية")
        search_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # استخدام QGridLayout للبحث
        from PyQt5.QtWidgets import QGridLayout, QSizePolicy
        search_layout = QGridLayout(search_group)
        search_layout.setSpacing(10)
        
        # البحث برقم اللوحة
        search_layout.addWidget(QLabel("رقم اللوحة:"), 0, 0)
        self.plate_search = QLineEdit()
        self.plate_search.setPlaceholderText("ابحث برقم اللوحة...")
        self.plate_search.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        search_layout.addWidget(self.plate_search, 0, 1)
        
        # البحث بالتاريخ
        search_layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.date_search = QDateEdit()
        self.date_search.setDate(QDate.currentDate())
        self.date_search.setCalendarPopup(True)
        self.date_search.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        search_layout.addWidget(self.date_search, 0, 3)
        
        # أزرار البحث
        buttons_layout = QHBoxLayout()
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_movements)
        self.search_button.setMinimumHeight(35)
        
        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_movements)
        self.refresh_button.setMinimumHeight(35)
        
        self.clear_search_button = QPushButton("مسح البحث")
        self.clear_search_button.clicked.connect(self.clear_search)
        self.clear_search_button.setMinimumHeight(35)
        
        buttons_layout.addWidget(self.search_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.clear_search_button)
        buttons_layout.addStretch()
        
        search_layout.addLayout(buttons_layout, 1, 0, 1, 4)
        
        layout.addWidget(search_group)
        
        # جدول الحركات مع تحسين المرونة
        self.movements_table = QTableWidget()
        self.movements_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setup_table()
        layout.addWidget(self.movements_table)
        
        # أزرار العمليات
        self.setup_action_buttons(layout)

        # تطبيق الأنماط
        self.apply_styles()

    def setup_action_buttons(self, layout):
        """إعداد أزرار العمليات"""
        buttons_layout = QHBoxLayout()

        # زر تصدير Excel
        self.export_button = QPushButton("تصدير إلى Excel")
        self.export_button.clicked.connect(self.export_to_excel)
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)

        # زر تصدير تقرير ملخص
        self.export_summary_button = QPushButton("تصدير تقرير ملخص")
        self.export_summary_button.clicked.connect(self.export_summary)
        self.export_summary_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        # عداد النتائج
        self.results_label = QLabel("عدد النتائج: 0")
        self.results_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                padding: 10px;
            }
        """)

        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.export_summary_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.results_label)

        layout.addLayout(buttons_layout)
    
    def setup_table(self):
        """إعداد جدول الحركات"""
        headers = ["رقم الحركة", "نقطة التفتيش", "رقم اللوحة", "اسم السائق", 
                  "رقم الهوية", "الاتجاه", "وقت الحركة", "ملاحظات"]
        
        self.movements_table.setColumnCount(len(headers))
        self.movements_table.setHorizontalHeaderLabels(headers)
        
        # تعديل خصائص الجدول
        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.movements_table.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.movements_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحركة
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # نقطة التفتيش
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم اللوحة
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # اسم السائق
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # رقم الهوية
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الاتجاه
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # وقت الحركة
        header.setSectionResizeMode(7, QHeaderView.Stretch)           # ملاحظات
    
    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # نمط حقول البحث
        search_style = """
            QLineEdit, QDateEdit, QPushButton {
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        
        for widget in [self.plate_search, self.date_search, self.search_button, 
                      self.refresh_button, self.clear_search_button]:
            widget.setStyleSheet(search_style)
        
        # نمط الجدول
        self.movements_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
            }
        """)
    
    def load_movements(self):
        """تحميل جميع الحركات"""
        try:
            movements = self.movement_model.get_movements_list(limit=1000)
            self.current_data = movements
            self.populate_table(movements)
            self.update_results_count(len(movements))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def search_movements(self):
        """البحث في الحركات"""
        try:
            plate_filter = self.plate_search.text().strip()
            date_filter = ""
            
            # التحقق من وجود تاريخ محدد
            if self.date_search.date() != QDate.currentDate():
                date_filter = self.date_search.date().toString("yyyy-MM-dd")
            
            movements = self.movement_model.get_movements_list(
                limit=1000, 
                plate_filter=plate_filter,
                date_filter=date_filter
            )
            
            self.current_data = movements
            self.populate_table(movements)
            self.update_results_count(len(movements))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث وإعادة تحميل جميع البيانات"""
        self.plate_search.clear()
        self.date_search.setDate(QDate.currentDate())
        self.load_movements()
    
    def populate_table(self, movements):
        """ملء الجدول بالبيانات"""
        self.movements_table.setRowCount(len(movements))
        
        for row, movement in enumerate(movements):
            # رقم الحركة
            self.movements_table.setItem(row, 0, QTableWidgetItem(str(movement['id'])))
            
            # نقطة التفتيش
            self.movements_table.setItem(row, 1, QTableWidgetItem(movement['checkpoint_name'] or ''))
            
            # رقم اللوحة
            self.movements_table.setItem(row, 2, QTableWidgetItem(movement['plate_number'] or ''))
            
            # اسم السائق
            self.movements_table.setItem(row, 3, QTableWidgetItem(movement['person_name'] or ''))
            
            # رقم الهوية
            self.movements_table.setItem(row, 4, QTableWidgetItem(movement['identity_number'] or ''))
            
            # الاتجاه
            self.movements_table.setItem(row, 5, QTableWidgetItem(movement['direction'] or ''))
            
            # وقت الحركة
            movement_time = movement['movement_time']
            if movement_time:
                # تحويل التاريخ إلى تنسيق مقروء
                try:
                    if isinstance(movement_time, str):
                        dt = datetime.fromisoformat(movement_time.replace('Z', '+00:00'))
                    else:
                        dt = movement_time
                    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    formatted_time = str(movement_time)
            else:
                formatted_time = ''
            self.movements_table.setItem(row, 6, QTableWidgetItem(formatted_time))
            
            # ملاحظات
            self.movements_table.setItem(row, 7, QTableWidgetItem(movement['notes'] or ''))
    
    def update_results_count(self, count):
        """تحديث عداد النتائج"""
        self.results_label.setText(f"عدد النتائج: {count}")
    
    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            if not self.current_data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات للتصدير")
                return
            
            filepath = self.excel_exporter.export_movements_to_excel(self.current_data)
            
            # فتح مجلد التقارير
            if os.path.exists(filepath):
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح:\n{filepath}")
                # فتح مجلد التقارير في مستكشف الملفات
                os.startfile(os.path.dirname(filepath))
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء ملف التقرير")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات: {str(e)}")
    
    def export_summary(self):
        """تصدير تقرير ملخص"""
        try:
            if not self.current_data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات للتصدير")
                return
            
            filepath = self.excel_exporter.export_summary_report(self.current_data)
            
            if os.path.exists(filepath):
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير الملخص بنجاح:\n{filepath}")
                # فتح مجلد التقارير في مستكشف الملفات
                os.startfile(os.path.dirname(filepath))
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء ملف التقرير")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير الملخص: {str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات (يتم استدعاؤها عند التبديل إلى هذه الصفحة)"""
        self.load_movements()


# نظام تسجيل الدخول - نظام إدارة المعبر الحدودي

## نظرة عامة

تم إضافة نظام تسجيل دخول شامل إلى تطبيق إدارة المعبر الحدودي باستخدام PyQt5 وقاعدة بيانات SQLite مع تشفير كلمات المرور باستخدام bcrypt.

## المميزات الجديدة

### 1. نظام المستخدمين
- **جدول المستخدمين**: يحتوي على id, username, password_hash, role, full_name
- **الأدوار المتاحة**:
  - `admin` (مدير): صلاحيات كاملة + إدارة المستخدمين
  - `operator` (مشغل): صلاحيات التشغيل العادية
  - `analyst` (محلل): صلاحيات التحليل والتقارير
  - `viewer` (مشاهد): صلاحيات المشاهدة فقط

### 2. نافذة تسجيل الدخول
- **واجهة عربية**: دعم كامل للغة العربية مع RTL
- **تصميم حديث**: واجهة مستخدم جذابة ومتجاوبة
- **التحقق الآمن**: استخدام bcrypt لتشفير والتحقق من كلمات المرور
- **رسائل خطأ واضحة**: تنبيهات مفيدة للمستخدم

### 3. إدارة المستخدمين (للمدراء فقط)
- **قائمة المستخدمين**: عرض جميع المستخدمين في جدول منظم
- **إضافة مستخدمين**: نافذة لإضافة مستخدمين جدد
- **حذف المستخدمين**: إمكانية حذف المستخدمين مع تأكيد
- **تحديث البيانات**: تحديث قائمة المستخدمين تلقائياً

## كيفية الاستخدام

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python main.py
```

### 3. تسجيل الدخول
- **المستخدم الافتراضي**: `admin`
- **كلمة المرور الافتراضية**: `admin123`

### 4. إدارة المستخدمين (للمدراء فقط)
1. سجل دخول كمدير
2. انقر على "إدارة المستخدمين" في القائمة الجانبية
3. استخدم زر "إضافة مستخدم" لإنشاء مستخدمين جدد
4. حدد مستخدم واضغط "حذف المستخدم" لحذفه

## الملفات الجديدة

### 1. `ui/login_dialog.py`
- نافذة تسجيل الدخول الرئيسية
- التحقق من بيانات المستخدم
- واجهة عربية مع دعم RTL

### 2. `ui/user_management_widget.py`
- واجهة إدارة المستخدمين
- نافذة إضافة مستخدم جديد
- جدول عرض المستخدمين

### 3. `test_login_system.py`
- اختبارات للتأكد من عمل النظام
- اختبار قاعدة البيانات
- اختبار واجهات المستخدم

## التعديلات على الملفات الموجودة

### 1. `database.py`
- إضافة جدول المستخدمين
- وظائف إدارة المستخدمين
- تشفير كلمات المرور باستخدام bcrypt
- إنشاء مستخدم مدير افتراضي

### 2. `ui/main_window.py`
- دعم بيانات المستخدم المسجل
- إضافة عنصر "إدارة المستخدمين" للمدراء
- عرض معلومات المستخدم الحالي

### 3. `main.py`
- عرض نافذة تسجيل الدخول قبل الواجهة الرئيسية
- تمرير بيانات المستخدم للواجهة الرئيسية
- التحقق من مكتبة passlib

### 4. `requirements.txt`
- إضافة مكتبة `passlib>=1.7.4`

## الأمان

- **تشفير كلمات المرور**: استخدام bcrypt لتشفير آمن
- **التحقق من الصلاحيات**: فحص دور المستخدم قبل عرض الوظائف
- **حماية قاعدة البيانات**: استخدام prepared statements لمنع SQL injection

## ملاحظات مهمة

1. **النسخ الاحتياطية**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات قبل إضافة مستخدمين جدد
2. **كلمات المرور**: يجب تغيير كلمة مرور المدير الافتراضية فور التشغيل الأول
3. **الصلاحيات**: تأكد من منح الصلاحيات المناسبة لكل مستخدم حسب دوره

## استكشاف الأخطاء

### مشكلة: "Package passlib is not installed"
**الحل**: قم بتثبيت المكتبة باستخدام:
```bash
pip install passlib
```

### مشكلة: "فشل في تسجيل الدخول"
**الحل**: تأكد من:
- صحة اسم المستخدم وكلمة المرور
- وجود قاعدة البيانات وجدول المستخدمين
- استخدام المستخدم الافتراضي: admin / admin123

### مشكلة: "لا يظهر عنصر إدارة المستخدمين"
**الحل**: تأكد من تسجيل الدخول بحساب مدير (role = 'admin')

## التطوير المستقبلي

- إضافة إمكانية تعديل بيانات المستخدمين
- نظام انتهاء صلاحية كلمات المرور
- سجل نشاطات المستخدمين
- إعدادات صلاحيات أكثر تفصيلاً
- نظام استرداد كلمة المرور

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox, QWidget,
                             QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from database import Database

class FixedLoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.db = Database()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        # إعدادات النافذة
        self.setWindowTitle("تسجيل الدخول - نظام إدارة المعبر الحدودي")
        self.setMinimumSize(450, 350)
        self.setMaximumSize(600, 500)
        self.resize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الخط
        font = QFont("Arial", 12)
        self.setFont(font)
        
        # التخطيط الرئيسي مع scroll area
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء scroll area للمحتوى
        from PyQt5.QtWidgets import QScrollArea, QSizePolicy
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QScrollArea.NoFrame)
        
        # محتوى قابل للتمرير
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(25)
        content_layout.setContentsMargins(40, 30, 40, 30)
        
        # العنوان الرئيسي
        title_label = QLabel("نظام إدارة المعبر الحدودي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        content_layout.addWidget(title_label)
        
        # عنوان فرعي
        subtitle = QLabel("تسجيل الدخول")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        content_layout.addWidget(subtitle)
        
        # إضافة مساحة مرنة
        content_layout.addSpacing(20)
        
        # مجموعة حقول الإدخال
        self.setup_input_fields(content_layout)
        
        # إضافة مساحة مرنة
        content_layout.addSpacing(20)
        
        # الأزرار
        self.setup_buttons(content_layout)
        
        # معلومات المستخدم الافتراضي
        info_label = QLabel("المستخدم الافتراضي: admin | كلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        content_layout.addWidget(info_label)
        
        # إضافة مساحة مرنة في النهاية
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # تطبيق الأنماط
        self.apply_styles()
        
        # ربط Enter بتسجيل الدخول
        self.username_edit.returnPressed.connect(self.handle_login)
        self.password_edit.returnPressed.connect(self.handle_login)
        
        # تركيز على اسم المستخدم
        self.username_edit.setFocus()

    def setup_input_fields(self, parent_layout):
        """إعداد حقول الإدخال بشكل مرن"""
        from PyQt5.QtWidgets import QSizePolicy
        
        # حاوية حقول الإدخال
        fields_widget = QWidget()
        fields_layout = QVBoxLayout(fields_widget)
        fields_layout.setSpacing(20)
        
        # اسم المستخدم
        username_container = QWidget()
        username_layout = QVBoxLayout(username_container)
        username_layout.setSpacing(8)
        username_layout.setContentsMargins(0, 0, 0, 0)
        
        username_label = QLabel("اسم المستخدم:")
        username_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        username_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.username_edit.setMinimumHeight(45)
        username_layout.addWidget(self.username_edit)
        
        fields_layout.addWidget(username_container)
        
        # كلمة المرور
        password_container = QWidget()
        password_layout = QVBoxLayout(password_container)
        password_layout.setSpacing(8)
        password_layout.setContentsMargins(0, 0, 0, 0)
        
        password_label = QLabel("كلمة المرور:")
        password_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        password_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.password_edit.setMinimumHeight(45)
        password_layout.addWidget(self.password_edit)
        
        fields_layout.addWidget(password_container)
        
        parent_layout.addWidget(fields_widget)

    def setup_buttons(self, parent_layout):
        """إعداد الأزرار بشكل مرن"""
        from PyQt5.QtWidgets import QSizePolicy
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.login_button.setMinimumHeight(45)
        self.login_button.setDefault(True)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.cancel_button.setMinimumHeight(45)
        
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)
        
        parent_layout.addLayout(buttons_layout)

    def apply_styles(self):
        """تطبيق الأنماط المحسنة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 1px solid #ddd;
            }
            
            QLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            
            QLabel[objectName="title"] {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
            
            QLabel[objectName="subtitle"] {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 10px;
            }
            
            QLabel[objectName="info"] {
                color: #7f8c8d;
                font-size: 11px;
                font-style: italic;
                margin-top: 15px;
                padding: 10px;
            }
            
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                min-height: 20px;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9ff;
            }
            
            QLineEdit:hover {
                border-color: #95a5a6;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QPushButton:default {
                background-color: #27ae60;
            }
            
            QPushButton:default:hover {
                background-color: #229954;
            }
            
            QPushButton[objectName="cancel_button"] {
                background-color: #95a5a6;
            }
            
            QPushButton[objectName="cancel_button"]:hover {
                background-color: #7f8c8d;
            }
            
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # تعيين أسماء الكائنات للأنماط
        self.cancel_button.setObjectName("cancel_button")
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            self.username_edit.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
            self.password_edit.setFocus()
            return
        
        try:
            # التحقق من بيانات المستخدم
            user = self.db.authenticate_user(username, password)
            
            if user:
                self.user_data = user
                QMessageBox.information(
                    self, 
                    "نجح تسجيل الدخول", 
                    f"مرحباً {user['full_name']}\nتم تسجيل الدخول بنجاح"
                )
                self.accept()
            else:
                QMessageBox.critical(
                    self, 
                    "فشل تسجيل الدخول", 
                    "اسم المستخدم أو كلمة المرور غير صحيحة"
                )
                self.password_edit.clear()
                self.username_edit.setFocus()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")
    
    def get_user_data(self):
        """الحصول على بيانات المستخدم المسجل"""
        return self.user_data




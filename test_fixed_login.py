#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from ui.fixed_login_dialog import FixedLoginDialog

def main():
    print("اختبار نافذة تسجيل الدخول المحسنة...")
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    dialog = FixedLoginDialog()
    
    print("تم إنشاء النافذة بنجاح")
    print("عرض النافذة...")
    
    # عرض النافذة
    result = dialog.exec_()
    
    if result == dialog.Accepted:
        user_data = dialog.get_user_data()
        if user_data:
            print(f"✓ تم تسجيل الدخول بنجاح")
            print(f"  المستخدم: {user_data['username']}")
            print(f"  الاسم: {user_data['full_name']}")
            print(f"  الدور: {user_data['role']}")
        else:
            print("✗ لم يتم الحصول على بيانات المستخدم")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

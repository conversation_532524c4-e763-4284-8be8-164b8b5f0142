#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon, QPixmap
from database import Database

class LoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.db = Database()
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة المعبر الحدودي")
        self.setFixedSize(450, 400)
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين الخط العربي
        font = QFont("Arial", 11)
        self.setFont(font)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(15)
        
        # عنوان النظام
        title_label = QLabel("نظام إدارة المعبر الحدودي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.StyledPanel)
        login_frame.setMinimumHeight(250)
        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(25, 25, 25, 25)
        login_layout.setSpacing(12)
        
        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 10px;
            }
        """)
        login_layout.addWidget(login_title)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        login_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setMinimumHeight(35)
        self.username_input.returnPressed.connect(self.login)
        login_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        login_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setMinimumHeight(35)
        self.password_input.returnPressed.connect(self.login)
        login_layout.addWidget(self.password_input)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.login)
        self.login_button.setDefault(True)
        self.login_button.setMinimumHeight(40)
        buttons_layout.addWidget(self.login_button)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setMinimumHeight(40)
        buttons_layout.addWidget(self.cancel_button)
        
        login_layout.addLayout(buttons_layout)
        main_layout.addWidget(login_frame)
        
        # مساحة فارغة
        main_layout.addStretch()
        
        # معلومات افتراضية
        info_label = QLabel("المستخدم الافتراضي: admin | كلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 10px;
                font-style: italic;
                padding: 5px;
            }
        """)
        main_layout.addWidget(info_label)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }

            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }

            QLineEdit {
                padding: 12px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                min-height: 20px;
            }

            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }

            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
                min-height: 25px;
            }

            QPushButton:hover {
                background-color: #0056b3;
            }

            QPushButton:pressed {
                background-color: #004085;
            }

            QPushButton#cancel_button {
                background-color: #6c757d;
            }

            QPushButton#cancel_button:hover {
                background-color: #545b62;
            }

            QLabel {
                color: #2c3e50;
            }
        """)

        # تطبيق معرف للزر إلغاء
        self.cancel_button.setObjectName("cancel_button")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # التحقق من بيانات المستخدم
        user = self.db.authenticate_user(username, password)
        
        if user:
            self.user_data = user
            QMessageBox.information(self, "نجح تسجيل الدخول", 
                                  f"مرحباً {user['full_name']}\nتم تسجيل الدخول بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "فشل تسجيل الدخول", 
                               "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.username_input.setFocus()
    
    def get_user_data(self):
        """الحصول على بيانات المستخدم المسجل"""
        return self.user_data

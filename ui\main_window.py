import sys
from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QStackedWidget, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon
from .movement_entry_widget import MovementEntryWidget
from .movements_view_widget import MovementsViewWidget
from .user_management_widget import UserManagementWidget

class MainWindow(QMainWindow):
    def __init__(self, user_data=None):
        super().__init__()
        self.user_data = user_data
        self.setWindowTitle("نظام إدارة المعبر الحدودي")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)  # دعم RTL

        # تطبيق الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)

        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)
        
        # إضافة الصفحات
        self.add_pages()
        
        # عرض الصفحة الأولى
        self.content_stack.setCurrentIndex(0)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(250)
        self.sidebar.setFrameStyle(QFrame.StyledPanel)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)
        
        # عنوان النظام
        title_label = QLabel("نظام إدارة المعبر الحدودي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 20px;
            }
        """)
        sidebar_layout.addWidget(title_label)
        
        # أزرار التنقل
        self.nav_buttons = []
        
        # زر إدخال حركة
        btn_entry = QPushButton("إدخال حركة جديدة")
        btn_entry.clicked.connect(lambda: self.switch_page(0))
        self.nav_buttons.append(btn_entry)
        sidebar_layout.addWidget(btn_entry)
        
        # زر عرض الحركات
        btn_view = QPushButton("عرض الحركات")
        btn_view.clicked.connect(lambda: self.switch_page(1))
        self.nav_buttons.append(btn_view)
        sidebar_layout.addWidget(btn_view)

        # زر إدارة المستخدمين (للمدراء فقط)
        if self.user_data and self.user_data.get('role') == 'admin':
            btn_users = QPushButton("إدارة المستخدمين")
            btn_users.clicked.connect(lambda: self.switch_page(2))
            self.nav_buttons.append(btn_users)
            sidebar_layout.addWidget(btn_users)

        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # معلومات المستخدم والنظام
        user_info = ""
        if self.user_data:
            user_info = f"المستخدم: {self.user_data.get('full_name', '')}\nالدور: {self.get_role_name(self.user_data.get('role', ''))}\n\n"

        info_label = QLabel(f"{user_info}الإصدار 1.0\nنظام إدارة معبر حدودي")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 10px;
                padding: 10px;
            }
        """)
        sidebar_layout.addWidget(info_label)
    
    def add_pages(self):
        """إضافة الصفحات إلى المحتوى الرئيسي"""
        # صفحة إدخال الحركة
        self.movement_entry_page = MovementEntryWidget()
        self.content_stack.addWidget(self.movement_entry_page)

        # صفحة عرض الحركات
        self.movements_view_page = MovementsViewWidget()
        self.content_stack.addWidget(self.movements_view_page)

        # صفحة إدارة المستخدمين (للمدراء فقط)
        if self.user_data and self.user_data.get('role') == 'admin':
            self.user_management_page = UserManagementWidget()
            self.content_stack.addWidget(self.user_management_page)
    
    def switch_page(self, index):
        """التبديل بين الصفحات"""
        self.content_stack.setCurrentIndex(index)
        
        # تحديث حالة الأزرار
        for i, btn in enumerate(self.nav_buttons):
            if i == index:
                btn.setStyleSheet(self.get_active_button_style())
            else:
                btn.setStyleSheet(self.get_button_style())
        
        # تحديث البيانات في صفحة عرض الحركات عند التبديل إليها
        if index == 1:
            self.movements_view_page.refresh_data()
        # تحديث البيانات في صفحة إدارة المستخدمين عند التبديل إليها
        elif index == 2 and hasattr(self, 'user_management_page'):
            self.user_management_page.load_users()
    
    def apply_styles(self):
        """تطبيق الأنماط على الواجهة"""
        # نمط الشريط الجانبي
        self.sidebar.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-right: 2px solid #dee2e6;
            }
        """)
        
        # نمط أزرار التنقل
        button_style = self.get_button_style()
        for btn in self.nav_buttons:
            btn.setStyleSheet(button_style)
        
        # تعيين الزر الأول كنشط
        if self.nav_buttons:
            self.nav_buttons[0].setStyleSheet(self.get_active_button_style())
        
        # نمط النافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
        """)
    
    def get_button_style(self):
        """نمط الأزرار العادية"""
        return """
            QPushButton {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 12px;
                text-align: center;
                font-size: 12px;
                color: #495057;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """
    
    def get_active_button_style(self):
        """نمط الزر النشط"""
        return """
            QPushButton {
                background-color: #007bff;
                border: 1px solid #007bff;
                border-radius: 5px;
                padding: 12px;
                text-align: center;
                font-size: 12px;
                color: white;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0056b3;
                border-color: #0056b3;
            }
        """

    def get_role_name(self, role):
        """الحصول على اسم الدور بالعربية"""
        role_names = {
            'admin': 'مدير',
            'operator': 'مشغل',
            'analyst': 'محلل',
            'viewer': 'مشاهد'
        }
        return role_names.get(role, role)
